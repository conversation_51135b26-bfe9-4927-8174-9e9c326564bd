

import numpy as np
import networkx as nx
import random
from collections import defaultdict, deque
from typing import Dict, List, Set, Tuple
import matplotlib.pyplot as plt

"""
缩小解空间程序

功能描述：
1.构建网络;
2.对每个节点赋予激活概率p，随机激活其邻居节点，例如节点n1激活其一阶邻居节点n2后，则n2的激活概率为p*p,激活范围为节点的三阶邻居以内，模拟后选择出三阶内概率最大的节点，
从原始网络中只保留删除选的节点，构建新的网络；
3.输出新的网络节点数和边数数据和原始网络的对应；
4.从新构建的网络中选择出度最大的前k=50个节点，概率p设定为0.05.

网络路径：
"""
network_path = "D:\\VS\\code\\networks\\blog.txt"
# network_path = "blog.txt"  # 相对路径，请确保文件存在

class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）

        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）

        Returns:
            生成的NetworkX无向图对象
        """
        G = nx.Graph()
        # 使用numpy加速边数据加载（跳过首行表头）
        edges_data = np.loadtxt(
            self.network_path,
            skiprows=1,    # 忽略标题行
            usecols=[0, 1] # 仅读取前两列作为边
        )
        # 将浮点数据转换为整数节点ID（适用于非负整数ID）
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)
        return G


def BPE(graph, S, p, cached_neighbors=None):
    """
    影响力评估 (Expected Diffusion Value)

    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        cached_neighbors: 缓存的邻居信息(未使用)

    Returns:
        float: 估计的影响力值
    """
    k = len(S)
    influence_sum = 0
    NS_1 = set()
    for node in S:
        if node in graph:
            NS_1.update(graph[node])  # 获取一阶邻居节点集合

    for node in NS_1:
        if node not in S:
            num_connections = sum(1 for s in S if s in graph[node])
            influence_sum += 1 - (1 - p) ** num_connections
    result = (k + influence_sum)    # 计算 EDV(S)

    return result


class NetworkActivationSimulator:
    """网络激活模拟器，用于缩小解空间"""

    def __init__(self, graph_generator: GEN_GRAPH, activation_prob: float = 0.05):
        """初始化激活模拟器

        Args:
            graph_generator: 图生成器实例
            activation_prob: 基础激活概率p
        """
        self.graph_gen = graph_generator
        self.G = graph_generator.nx_G
        self.neighbors = graph_generator.neighbors
        self.p = activation_prob

    def get_k_hop_neighbors(self, node: int, k: int) -> Dict[int, int]:
        """获取节点的k阶邻居及其距离

        Args:
            node: 起始节点
            k: 最大跳数

        Returns:
            字典 {邻居节点: 距离}
        """
        if node not in self.G.nodes():
            return {}

        visited = {node: 0}
        queue = deque([(node, 0)])

        while queue:
            current_node, distance = queue.popleft()

            if distance < k:
                for neighbor in self.neighbors.get(current_node, []):
                    if neighbor not in visited:
                        visited[neighbor] = distance + 1
                        queue.append((neighbor, distance + 1))

        # 移除起始节点本身
        visited.pop(node, None)
        return visited

    def calculate_activation_probabilities(self, source_node: int) -> Dict[int, float]:
        """计算从源节点开始的三阶邻居激活概率

        Args:
            source_node: 源激活节点

        Returns:
            字典 {节点: 激活概率}
        """
        k_hop_neighbors = self.get_k_hop_neighbors(source_node, 3)
        activation_probs = {}

        for neighbor, distance in k_hop_neighbors.items():
            # 激活概率 = p^distance
            activation_probs[neighbor] = self.p ** distance

        return activation_probs

    def simulate_network_activation_with_bpe(self, num_simulations: int = 1000) -> Set[int]:
        """使用BP评估逻辑模拟网络激活过程，选择高影响力节点

        Args:
            num_simulations: 模拟次数

        Returns:
            选中的高影响力节点集合
        """
        node_influence_scores = defaultdict(float)
        all_nodes = list(self.G.nodes())

        print(f"开始基于BP评估的网络激活模拟，共{num_simulations}次...")

        for sim in range(num_simulations):
            if (sim + 1) % 100 == 0:
                print(f"完成模拟 {sim + 1}/{num_simulations}")

            # 随机选择种子节点集合（1-3个节点）
            seed_size = random.randint(1, min(3, len(all_nodes)))
            seed_nodes = set(random.sample(all_nodes, seed_size))

            # 使用BPE计算影响力
            influence_score = BPE(self.G, seed_nodes, self.p)

            # 将影响力分配给种子节点
            for node in seed_nodes:
                node_influence_scores[node] += influence_score / len(seed_nodes)

        # 计算平均影响力得分
        avg_influence_scores = {
            node: score / num_simulations
            for node, score in node_influence_scores.items()
        }

        # 选择影响力最高的节点（前50%）
        if avg_influence_scores:
            sorted_nodes = sorted(avg_influence_scores.items(), key=lambda x: x[1], reverse=True)
            threshold_idx = len(sorted_nodes) // 2
            selected_nodes = {node for node, _ in sorted_nodes[:threshold_idx]}

            print(f"基于BP评估选择了 {len(selected_nodes)} 个高影响力节点")
            return selected_nodes
        else:
            print("未找到影响力节点")
            return set()

    def create_reduced_network(self, selected_nodes: Set[int]) -> nx.Graph:
        """基于选中节点创建缩小的网络

        Args:
            selected_nodes: 选中的节点集合

        Returns:
            缩小后的网络图
        """
        # 创建子图，只包含选中的节点
        reduced_graph = self.G.subgraph(selected_nodes).copy()

        print(f"原始网络: {self.G.number_of_nodes()} 个节点, {self.G.number_of_edges()} 条边")
        print(f"缩小网络: {reduced_graph.number_of_nodes()} 个节点, {reduced_graph.number_of_edges()} 条边")

        return reduced_graph

    def get_top_k_degree_nodes(self, graph: nx.Graph, k: int = 50) -> List[Tuple[int, int]]:
        """获取出度最大的前k个节点

        Args:
            graph: 网络图
            k: 选择的节点数量

        Returns:
            [(节点, 度数)] 的列表
        """
        degree_list = [(node, degree) for node, degree in graph.degree()]
        degree_list.sort(key=lambda x: x[1], reverse=True)

        top_k = degree_list[:min(k, len(degree_list))]

        print(f"\n前{len(top_k)}个最高度数节点:")
        for i, (node, degree) in enumerate(top_k[:10], 1):  # 只显示前10个
            print(f"{i}. 节点 {node}: 度数 {degree}")
        if len(top_k) > 10:
            print(f"... 还有 {len(top_k) - 10} 个节点")

        return top_k

    def analyze_network_comparison(self, original_graph: nx.Graph, reduced_graph: nx.Graph):
        """分析原始网络与缩小网络的对比

        Args:
            original_graph: 原始网络
            reduced_graph: 缩小后的网络
        """
        print("\n" + "="*50)
        print("网络对比分析")
        print("="*50)

        # 基本统计
        orig_nodes = original_graph.number_of_nodes()
        orig_edges = original_graph.number_of_edges()
        red_nodes = reduced_graph.number_of_nodes()
        red_edges = reduced_graph.number_of_edges()

        print(f"节点数量: {orig_nodes} -> {red_nodes} (保留 {red_nodes/orig_nodes*100:.1f}%)")
        print(f"边数量: {orig_edges} -> {red_edges} (保留 {red_edges/orig_edges*100:.1f}%)")

        # 度数统计
        orig_avg_degree = sum(dict(original_graph.degree()).values()) / orig_nodes
        red_avg_degree = sum(dict(reduced_graph.degree()).values()) / red_nodes if red_nodes > 0 else 0

        print(f"平均度数: {orig_avg_degree:.2f} -> {red_avg_degree:.2f}")

        # 连通性分析
        orig_components = nx.number_connected_components(original_graph)
        red_components = nx.number_connected_components(reduced_graph) if red_nodes > 0 else 0

        print(f"连通分量数: {orig_components} -> {red_components}")

        if red_nodes > 0:
            # 最大连通分量大小
            orig_largest_cc = len(max(nx.connected_components(original_graph), key=len))
            red_largest_cc = len(max(nx.connected_components(reduced_graph), key=len))
            print(f"最大连通分量: {orig_largest_cc} -> {red_largest_cc}")

    def save_reduced_network(self, reduced_graph: nx.Graph, filename: str = "new_blog.txt"):
        """将缩小的网络保存为边列表文件

        Args:
            reduced_graph: 缩小后的网络图
            filename: 保存的文件名
        """
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write("source target\n")

            # 写入所有边
            for edge in reduced_graph.edges():
                f.write(f"{edge[0]} {edge[1]}\n")

        print(f"缩小网络已保存为 '{filename}'")
        print(f"包含 {reduced_graph.number_of_nodes()} 个节点, {reduced_graph.number_of_edges()} 条边")

    def visualize_networks(self, original_graph: nx.Graph, reduced_graph: nx.Graph,
                          selected_nodes: Set[int], save_plots: bool = True):
        """可视化原始网络和缩小网络的对比

        Args:
            original_graph: 原始网络
            reduced_graph: 缩小后的网络
            selected_nodes: 选中的节点集合
            save_plots: 是否保存图片
        """
        # 设置中文字体，避免字体警告
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 原始网络可视化
        pos1 = nx.spring_layout(original_graph, seed=42)

        # 区分选中和未选中的节点
        selected_in_orig = [node for node in original_graph.nodes() if node in selected_nodes]
        unselected_in_orig = [node for node in original_graph.nodes() if node not in selected_nodes]

        # 绘制原始网络
        nx.draw_networkx_edges(original_graph, pos1, ax=ax1, alpha=0.3, edge_color='gray')
        nx.draw_networkx_nodes(original_graph, pos1, nodelist=unselected_in_orig,
                             node_color='lightblue', node_size=100, ax=ax1)
        nx.draw_networkx_nodes(original_graph, pos1, nodelist=selected_in_orig,
                             node_color='red', node_size=200, ax=ax1)

        # 使用英文标题避免字体问题
        ax1.set_title(f'Original Network\n({original_graph.number_of_nodes()} nodes, {original_graph.number_of_edges()} edges)')
        ax1.axis('off')

        # 缩小网络可视化
        if reduced_graph.number_of_nodes() > 0:
            pos2 = nx.spring_layout(reduced_graph, seed=42)

            # 根据度数设置节点大小
            node_sizes = [reduced_graph.degree(node) * 50 + 100 for node in reduced_graph.nodes()]

            nx.draw_networkx_edges(reduced_graph, pos2, ax=ax2, alpha=0.5, edge_color='blue')
            nx.draw_networkx_nodes(reduced_graph, pos2, node_color='orange',
                                 node_size=node_sizes, ax=ax2)
            nx.draw_networkx_labels(reduced_graph, pos2, ax=ax2, font_size=8)

            ax2.set_title(f'Reduced Network\n({reduced_graph.number_of_nodes()} nodes, {reduced_graph.number_of_edges()} edges)')
        else:
            ax2.text(0.5, 0.5, 'No nodes', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('Reduced Network\n(0 nodes, 0 edges)')

        ax2.axis('off')

        plt.tight_layout()

        if save_plots:
            plt.savefig('network_comparison.png', dpi=300, bbox_inches='tight')
            print("网络对比图已保存为 'network_comparison.png'")

        plt.show()


def main():
    """主函数"""
    print("缩小解空间程序启动")
    print("="*50)

    # 1. 构建网络
    print("1. 构建网络...")
    graph_gen = GEN_GRAPH(network_path)
    print(f"成功加载网络: {graph_gen.nx_G.number_of_nodes()} 个节点, {graph_gen.nx_G.number_of_edges()} 条边")

    # 2. 初始化激活模拟器
    print("\n2. 初始化激活模拟器...")
    simulator = NetworkActivationSimulator(graph_gen, activation_prob=0.05)

    # 3. 使用BP评估模拟网络激活
    print("\n3. 使用BP评估模拟网络激活过程...")
    selected_nodes = simulator.simulate_network_activation_with_bpe(num_simulations=500)

    if not selected_nodes:
        print("未选择到任何节点，程序结束")
        return

    # 4. 创建缩小的网络
    print("\n4. 创建缩小的网络...")
    reduced_graph = simulator.create_reduced_network(selected_nodes)

    # 5. 分析网络对比
    print("\n5. 分析网络对比...")
    simulator.analyze_network_comparison(graph_gen.nx_G, reduced_graph)

    # 6. 选择出度最大的前k个节点
    print("\n6. 选择出度最大的前50个节点...")
    top_k_nodes = simulator.get_top_k_degree_nodes(reduced_graph, k=50)

    # 7. 可视化网络对比（可选）
    print("\n7. 生成网络可视化...")
    simulator.visualize_networks(graph_gen.nx_G, reduced_graph, selected_nodes, save_plots=True)

    print(f"\n程序执行完成！")
    print(f"最终选择了 {len(top_k_nodes)} 个高度数节点")

    return {
        'original_graph': graph_gen.nx_G,
        'reduced_graph': reduced_graph,
        'top_k_nodes': top_k_nodes,
        'selected_nodes': selected_nodes
    }


if __name__ == "__main__":
    result = main()