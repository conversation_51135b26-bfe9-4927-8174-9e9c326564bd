# 缩小解空间程序

## 功能概述

这个程序实现了基于网络激活概率的解空间缩小算法，主要功能包括：

1. **网络构建**: 从边列表文件构建网络图
2. **激活模拟**: 模拟节点激活过程，计算三阶邻居内的激活概率
3. **网络缩小**: 基于激活概率选择重要节点，构建缩小的网络
4. **结果分析**: 对比原始网络与缩小网络的统计特性
5. **高度数节点选择**: 从缩小网络中选择出度最大的前k个节点
6. **可视化**: 生成网络对比图

## 算法原理

### 激活概率计算
- 每个节点被赋予基础激活概率 p = 0.05
- 节点激活其邻居时，激活概率按距离衰减：p^distance
- 激活范围限制在三阶邻居以内

### 网络缩小策略
- 通过多次随机激活模拟，统计每个节点的平均激活概率
- 选择激活概率最高的前50%节点构建新网络
- 保留选中节点间的所有连边

## 文件结构

```
├── 缩小解空间.py          # 主程序文件
├── blog.txt              # 示例网络数据文件
├── network_comparison.png # 生成的网络对比图
└── README.md             # 说明文档
```

## 使用方法

### 1. 准备网络数据文件

网络数据文件格式要求：
- 第一行为表头（会被自动跳过）
- 每行包含两个节点ID，表示一条边
- 节点ID应为非负整数

示例格式：
```
source target
0 1
0 2
1 2
...
```

### 2. 修改网络路径

在程序中修改 `network_path` 变量：
```python
network_path = "your_network_file.txt"
```

### 3. 运行程序

```bash
python 缩小解空间.py
```

### 4. 调整参数

可以调整的主要参数：
- `activation_prob`: 基础激活概率（默认0.05）
- `num_simulations`: 模拟次数（默认500）
- `k`: 选择的高度数节点数量（默认50）

## 输出结果

程序会输出以下信息：

1. **网络加载信息**: 原始网络的节点数和边数
2. **激活模拟进度**: 显示模拟进度
3. **网络缩小结果**: 缩小后网络的节点数和边数
4. **对比分析**: 详细的网络统计对比
5. **高度数节点**: 前k个最高度数节点列表
6. **可视化图片**: 保存网络对比图为PNG文件

## 程序特点

### 优势
- **高效的邻居查询**: 预计算并缓存所有节点的邻居关系
- **概率驱动的选择**: 基于激活概率而非简单的度数进行节点选择
- **多层次分析**: 提供详细的网络统计和对比分析
- **可视化支持**: 自动生成网络对比图

### 适用场景
- 大规模网络的降维处理
- 社交网络关键节点识别
- 生物网络核心模块提取
- 交通网络重要节点分析

## 技术依赖

- Python 3.7+
- NetworkX: 网络分析
- NumPy: 数值计算
- Matplotlib: 可视化
- Collections: 数据结构

## 注意事项

1. 确保网络数据文件格式正确
2. 对于大型网络，可能需要调整模拟次数以平衡精度和效率
3. 可视化功能在中文环境下可能有字体警告，但不影响功能
4. 程序会自动处理不连通的网络组件

## 扩展建议

- 支持有向图分析
- 添加更多的网络中心性指标
- 实现并行化模拟以提高效率
- 支持动态网络分析
